<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tip_parent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_center_popup"
    android:padding="@dimen/dp_15"
    tools:context=".ui.activities.mingle.MatchTipDialogActivity">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/anchor_image"
        android:layout_width="@dimen/dp_270"
        android:layout_height="@dimen/dp_279"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/CustomShapeAppearance"
        app:strokeColor="@null" />

    <TextView
        android:id="@+id/anchor_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_13"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/anchor_image" />

    <TextView
        android:id="@+id/anchor_age_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/anchor_name" />


    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/shape_dialog_btn_exit"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_16"
        app:layout_constraintEnd_toStartOf="@id/btn_ok"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/anchor_age_country" />


    <LinearLayout
        android:id="@+id/btn_ok"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_48"
        android:background="@drawable/shape_primary_button"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="2"
        app:layout_constraintStart_toEndOf="@id/btn_cancel">

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/anchor_video"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_32"
            app:autoPlay="true"
            app:source="ic_video_small.svga" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/video_call"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/videoPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:drawablePadding="@dimen/dp_2"
                android:gravity="center_vertical"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10"
                app:drawableStartCompat="@mipmap/ic_diamond" />

        </LinearLayout>


    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>