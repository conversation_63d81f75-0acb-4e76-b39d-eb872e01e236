<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_12"
    android:background="@drawable/shape_home_renew"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_6"
    android:paddingVertical="@dimen/dp_6">

    <ImageView
        android:id="@+id/tag"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:adjustViewBounds="true"
        android:layout_marginStart="@dimen/dp_6"
        android:src="@mipmap/ic_small_bell" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_weight="1"
        android:text="@string/vip_will_expire_tips"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10" />

    <TextView
        android:id="@+id/tv_renew"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_5"
        android:background="@drawable/shape_white_radius50"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_0_5"
        android:text="@string/renew"
        android:textAllCaps="false"
        android:textColor="@color/color_28B8E4"
        android:textSize="@dimen/sp_10" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_15"
        android:layout_height="@dimen/dp_15"
        android:padding="@dimen/dp_4"
        android:src="@mipmap/ic_close_black"
        app:tint="@color/white" />
</LinearLayout>