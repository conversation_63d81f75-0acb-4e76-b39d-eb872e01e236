package com.heart.heartmerge.manager

import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.beans.VirtualVideoBean
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.utils.ContextHolder
import com.liulishuo.okdownload.DownloadListener
import com.liulishuo.okdownload.DownloadTask
import com.liulishuo.okdownload.core.breakpoint.BreakpointInfo
import com.liulishuo.okdownload.core.cause.EndCause
import com.liulishuo.okdownload.core.cause.ResumeFailedCause
import java.io.File


/**
 * 作者：Lxf
 * 创建日期：2024/8/27 15:19
 * 描述：预热视频下载管理
 */
object VirtualVideoManager {
    const val SAVE_VIDEO_FOLDER = "virtualVideos"
    private val urlList = mutableListOf<VirtualVideoBean>()
    fun enqueueDownload(videos: MutableList<VirtualVideoBean>) {
        if (videos.isEmpty()) {
            Logger.e("VirtualVideoManager: videoUrl is null")
            return
        }
        urlList.clear()
        urlList.addAll(videos)
        startDownload(urlList[0])
    }

    private fun startDownload(videoBean: VirtualVideoBean) {
        if (videoBean.url.isEmpty()){
            if (urlList.isNotEmpty()) {
                urlList.removeAt(0)
                if (urlList.isNotEmpty()) {
                    startDownload(urlList[0])
                }
            }
        }else {
            Logger.d("startDownload: ")
            val downloadFolder = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
            if (!downloadFolder.exists()) downloadFolder.mkdirs()
            val tempFileName = "${videoBean.id}.mp4.downloading"
            val finalFileName = "${videoBean.id}.mp4"
            val downloadTask = DownloadTask.Builder(videoBean.url, downloadFolder)
                .setFilename(tempFileName)
                .setMinIntervalMillisCallbackProcess(1500)
                .setPassIfAlreadyCompleted(true)
                .setConnectionCount(1)
                .build()

            downloadTask.enqueue(object : DownloadListener {
                override fun taskStart(task: DownloadTask) {}
                override fun connectTrialStart(task: DownloadTask, requestHeaderFields: Map<String, List<String>>) {}
                override fun connectTrialEnd(task: DownloadTask, responseCode: Int, responseHeaderFields: MutableMap<String, MutableList<String>>) {}
                override fun downloadFromBeginning(task: DownloadTask, info: BreakpointInfo, cause: ResumeFailedCause) {}
                override fun downloadFromBreakpoint(task: DownloadTask, info: BreakpointInfo) {}
                override fun connectStart(task: DownloadTask, blockIndex: Int, requestHeaderFields: Map<String, List<String>>) {}
                override fun connectEnd(task: DownloadTask, blockIndex: Int, responseCode: Int, responseHeaderFields: MutableMap<String, MutableList<String>>) {}
                override fun fetchStart(task: DownloadTask, blockIndex: Int, contentLength: Long) {}
                override fun fetchProgress(task: DownloadTask, blockIndex: Int, increaseBytes: Long) {
                    Logger.d("fetchProgress: $blockIndex  $increaseBytes")
                }

                override fun fetchEnd(task: DownloadTask, blockIndex: Int, contentLength: Long) {}
                override fun taskEnd(task: DownloadTask, cause: EndCause, realCause: Exception?) {
                    when (cause) {
                        EndCause.COMPLETED -> {
                            Logger.d("taskEnd:COMPLETED ")
                            val tempFile = File(downloadFolder, tempFileName)
                            val finalFile = File(downloadFolder, finalFileName)
                            if (tempFile.exists()) {
                                tempFile.renameTo(finalFile)
                            }
                            //递归下载
                            if (urlList.isNotEmpty()) {
                                urlList.removeAt(0)
                                if (urlList.isNotEmpty()) {
                                    startDownload(urlList[0])
                                }
                            }
                        }

                        EndCause.ERROR -> {
                            ReportManager.log("VirtualVideoManager taskEnd: ERROR ${realCause?.message}")
                            Logger.e("taskEnd: ERROR ${realCause?.message}")
                            task.file?.apply {
                                delete()
                            }
                            if (urlList.isNotEmpty()) {
                                startDownload(urlList[0])
                            }
                        }

                        else -> {}
                    }
                }
            })
        }
    }
}