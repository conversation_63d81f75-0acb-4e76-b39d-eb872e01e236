package com.heart.heartmerge.manager

import android.content.Intent
import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.beans.ChatTipBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoPlayBean
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.IncomingVideoActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.toJson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

/**
 * Author:Lxf
 * Create on:2024/8/27
 * Description:
 */
object VideoPlaybackController {
    private val playbackQueue: MutableList<VideoPlayBean> = mutableListOf()
    private var lastPlayTime: Long = 0
    private var loopJob: Job? = null

    fun addToPlaybackQueue(videoUrl: String, lifecycle: CoroutineScope, chatTipBean: ChatTipBean) {
        val videoPlayBean = VideoPlayBean(videoUrl, chatTipBean.isNeedMute, chatTipBean.isNeedBal, chatTipBean.id, chatTipBean.videoPrice, chatTipBean.videoId, chatTipBean.recordId)
        if (playbackQueue.indexOf(videoPlayBean) == -1) {
            playbackQueue.add(videoPlayBean)
        }

        if (loopJob == null) { //确保只用一个携程
            loopJob = lifecycle.launch {
                startPlayLoop(lifecycle)
            }
        }
    }

    private suspend fun startPlayLoop(lifecycle: CoroutineScope) {
        Logger.i("VideoPlaybackController startPlayLoop ")
        if (AppUtil.canAutoPopupVideoCallingPage) {
            //可以播放
            if (playbackQueue.isEmpty()) {
                //关闭携程 停止轮询 当有新数据 携程重新创建
                loopJob?.cancel()
                loopJob = null
            } else {
                val playVideo = playbackQueue.removeAt(0)
                val delay = calculatePlayDelay()
                delay(delay)
                playVideo(playVideo)
                lastPlayTime = System.currentTimeMillis()
            }
        }
        //正在播放视频 延迟检测
        delay(10000)
        startPlayLoop(lifecycle)
    }

    private fun calculatePlayDelay(): Long {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastPlay = currentTime - lastPlayTime
        return if (timeSinceLastPlay < 20_000L) {
            20_000L - timeSinceLastPlay
        } else {
            0L
        }
    }

    private fun playVideo(videoPlayBean: VideoPlayBean) {
        val filePath = videoPlayBean.url
        val file = File(filePath)
        if (file.exists()) {
            if (MMKVDataRep.doNotDisturb) { //设置了免打扰 删除文件 不播放
                file.delete()
            } else {
                Logger.i("VideoPlaybackController playVideo $filePath")
                ContextHolder.context.getLifecycleCallbacks()?.apply {
                    if (AppUtil.canAutoPopupVideoCallingPage) {
                        if (activityList.isNotEmpty()) {
                            val activity = activityList[0]
                            if (activity is AnchorVideoActivity){
                                Logger.i("AnchorVideoActivity is top")
                            }else {
                                // 通过 Intent 启动播放器页面，并传递视频的 URI
                                val intent = Intent(ContextHolder.context, IncomingVideoActivity::class.java)
                                val tmpUser = UserBean(id = videoPlayBean.id, virVideoId = videoPlayBean.videoId)
                                intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, tmpUser.toJson())
                                intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, filePath)
                                intent.putExtra(Constants.INTENT_PARAM_ISNEEDMUTE, videoPlayBean.isNeedMute)
                                intent.putExtra(Constants.INTENT_PARAM_ISNEEDBAL, videoPlayBean.isNeedBal)
                                intent.putExtra(Constants.INTENT_PARAM_VIDEO_PRICE, videoPlayBean.videoPrice)
                                intent.putExtra(Constants.INTENT_PARAM_RECORD_ID, videoPlayBean.recordId)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                activity.startActivity(intent)
                            }

                        }
                    }
                }
//                // 通过 Intent 启动播放器页面，并传递视频的 URI
//                val intent = Intent(ContextHolder.context, IncomingVideoActivity::class.java)
//                val tmpUser = UserBean(id = file.nameWithoutExtension)
//                intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, tmpUser.toJson())
//                intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, filePath)
//                intent.putExtra(Constants.INTENT_PARAM_ISNEEDMUTE, isNeedMute)
//                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//                ContextHolder.context.startActivity(intent)
            }

        } else {
            Logger.e("playVideo file not exists ")
        }
    }
}