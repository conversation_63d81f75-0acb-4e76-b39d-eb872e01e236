package com.heart.heartmerge.manager

import com.bdc.android.library.utils.Logger
import com.heart.heartmerge.beans.ChatTipBean
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.utils.ContextHolder
import com.liulishuo.okdownload.DownloadListener
import com.liulishuo.okdownload.DownloadTask
import com.liulishuo.okdownload.core.breakpoint.BreakpointInfo
import com.liulishuo.okdownload.core.cause.EndCause
import com.liulishuo.okdownload.core.cause.ResumeFailedCause
import kotlinx.coroutines.CoroutineScope
import java.io.File


/**
 * 作者：Lxf
 * 创建日期：2024/8/27 15:19
 * 描述：管理通知过来的视频
 */
object NotifyVideoManager {
    private val taskList = mutableListOf<DownloadTask>()
    const val saveVideoFolder = "downloadedVideos"
    fun enqueueDownload(lifecycle: CoroutineScope, chatTipBean: ChatTipBean) {
        if (chatTipBean.content.isEmpty() || chatTipBean.id.isEmpty()) {
            Logger.e("NotifyVideoManager: videoUrl or userId is null")
            return
        }
        startDownload(lifecycle, chatTipBean)
    }

    private fun startDownload(lifecycle: CoroutineScope, chatTipBean: ChatTipBean) {
        Logger.d("startDownload: ")
        val fileName = if (chatTipBean.recordId.isNotEmpty()) chatTipBean.recordId else chatTipBean.id
        Logger.e("startDownload: $fileName")
        val downloadTask = DownloadTask.Builder(
            chatTipBean.content, File(ContextHolder.context.filesDir, saveVideoFolder)
        ).setFilename("${fileName}.mp4").setMinIntervalMillisCallbackProcess(1500)
            .setPassIfAlreadyCompleted(true).setConnectionCount(1).build()

        downloadTask.enqueue(object : DownloadListener {
            override fun taskStart(task: DownloadTask) {}
            override fun connectTrialStart(
                task: DownloadTask, requestHeaderFields: Map<String, List<String>>
            ) {
            }

            override fun connectTrialEnd(
                task: DownloadTask,
                responseCode: Int,
                responseHeaderFields: MutableMap<String, MutableList<String>>
            ) {
            }

            override fun downloadFromBeginning(
                task: DownloadTask, info: BreakpointInfo, cause: ResumeFailedCause
            ) {
            }

            override fun downloadFromBreakpoint(task: DownloadTask, info: BreakpointInfo) {}

            override fun connectStart(
                task: DownloadTask, blockIndex: Int, requestHeaderFields: Map<String, List<String>>
            ) {
            }

            override fun connectEnd(
                task: DownloadTask,
                blockIndex: Int,
                responseCode: Int,
                responseHeaderFields: MutableMap<String, MutableList<String>>
            ) {
            }

            override fun fetchStart(task: DownloadTask, blockIndex: Int, contentLength: Long) {}
            override fun fetchProgress(task: DownloadTask, blockIndex: Int, increaseBytes: Long) {
                Logger.d("fetchProgress: $blockIndex  $increaseBytes")
            }

            override fun fetchEnd(task: DownloadTask, blockIndex: Int, contentLength: Long) {}
            override fun taskEnd(task: DownloadTask, cause: EndCause, realCause: Exception?) {
                when (cause) {
                    EndCause.COMPLETED -> {
                        Logger.d("taskEnd:COMPLETED ")
                        // 将下载的视频添加到播放队列中
                        task.file?.apply {
                            onDownloadComplete(lifecycle, path, chatTipBean)
                        }
                    }

                    EndCause.ERROR -> {
                        ReportManager.log("NotifyVideoManager taskEnd: ERROR ${realCause?.message}")
                        Logger.e("taskEnd: ERROR ${realCause?.message}")
                        task.file?.apply {
                            delete()
                        }
                    }

                    else -> {}
                }
            }
        })
        taskList.add(downloadTask)
    }

    private fun onDownloadComplete(
        lifecycle: CoroutineScope, localFilePath: String, chatTipBean: ChatTipBean
    ) {
        // 下载完成后添加到播放队列
        VideoPlaybackController.addToPlaybackQueue(localFilePath, lifecycle, chatTipBean)
    }
}