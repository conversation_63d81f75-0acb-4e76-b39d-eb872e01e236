package com.heart.heartmerge.ui.activities.message

import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.VideoRecordInfo
import com.heart.heartmerge.databinding.ActivityVideoHistoryBinding
import com.heart.heartmerge.extension.openLoadMore
import com.heart.heartmerge.viewmodes.VideoHistoryRequestEvent
import com.heart.heartmerge.viewmodes.VideoHistoryViewModel

/**
 * Author:Lxf
 * Create on:2024/8/17
 * Description:
 */
class VideoHistoryActivity : BaseCoreActivity<ActivityVideoHistoryBinding, VideoHistoryViewModel>() {
    private var loadPage = 1
    private val pageSize = 20
    override fun getLayoutId(): Int = R.layout.activity_video_history
    override fun initView() {
        mBinding.refreshLayout.recyclerView.apply {
            setOnRefreshListener { b: <PERSON><PERSON><PERSON>, _: Int ->
                if (b) {
                    loadPage = 1
                }
                mViewModel.videoHistoryList(loadPage, pageSize)
            }
            openLoadMore {
                loadPage++
                mViewModel.videoHistoryList(loadPage, pageSize)
            }
        }
    }

    override fun initData() {
        mBinding.refreshLayout.onRefresh()
    }

    override fun initViewEvents() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is VideoHistoryRequestEvent.GetVideoHistorySuccess -> {
                    mBinding.refreshLayout.apply {
                        if (loadPage == 1) {
                            clearAllItems()
                        }
                        append<VideoHistoryItem>(items = it.list, isNoMore = it.list.isNullOrEmpty()) { data ->
                            recordInfo = data as VideoRecordInfo
                        }
                    }
                }

                is VideoHistoryRequestEvent.GetVideoHistoryFailed -> {
                    ToastUtil.show(it.msg)
                    val tmpPage = loadPage--
                    loadPage = if (tmpPage >= 1) tmpPage else 1
                    mBinding.refreshLayout.loadFailedWithMore()
                }
            }
        }
    }
}