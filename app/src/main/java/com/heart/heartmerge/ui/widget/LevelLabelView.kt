package com.heart.heartmerge.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ViewLevelLabelBinding

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/8 16:47
 * @description :等级标签View
 */
class LevelLabelView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    val binding: ViewLevelLabelBinding =
        ViewLevelLabelBinding.inflate(LayoutInflater.from(this.context), this, true)

    var current: Int = 0
        set(value) {
            field = if (value > 35) 35 else value
            binding.tvLevel.text = "Lv$field"
            when (field) {
                in 0..5 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_1)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_1)
                }

                in 6..10 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_11)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_11)
                }

                in 11..15 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_21)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_21)
                }

                in 16..20 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_31)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_31)
                }

                in 21..25 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_41)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_41)
                }

                in 26..30 -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_51)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_51)
                }

                else -> {
                    binding.tvLevel.setBackgroundResource(R.mipmap.bg_level_label_61)
                    binding.tvLevel.setTextCompatColor(R.color.color_level_61)
                }
            }
        }
}