package com.heart.heartmerge.ui.widget

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import com.heart.heartmerge.extension.dp
import com.heart.heartmerge.extension.sp
import kotlin.math.abs

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/8 18:00
 * @description :等级步骤条View
 */
class LevelStepView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val levels = arrayListOf<String>("", "Lv0-10", "Lv11-20", "Lv21-30", "Lv31-40", "")
    private var selectedLevelIndex = 1
    private var animatedIndex = selectedLevelIndex.toFloat()
    private val selectedCircleRadius = 5.dp
    private val levelPoints = mutableListOf<Float>()
    private var fadeAnimator: ValueAnimator? = null
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        style = Paint.Style.FILL
        maskFilter = BlurMaskFilter(3.dp.toFloat(), BlurMaskFilter.Blur.NORMAL)
    }

    init {
        paint.textSize = 26.sp
        paint.strokeCap = Paint.Cap.ROUND
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val lineY = 10.dp.toFloat()  // 改为固定距离，从顶部开始
        val startX = 0f
        val endX = width * 1f

        // 计算每个区间点的 X 位置
        levelPoints.clear()
        for (i in levels.indices) {
            levelPoints.add(startX + i * (endX - startX) / (levels.size - 1))
        }

        // 计算选中点的位置
        val selectedX = calculateSelectedX()

        // 绘制带渐变的线
        drawGradientLine(canvas, lineY, selectedX)

        // 绘制点和标签
        drawCirclesAndLabels(canvas, lineY, selectedX)
    }

    private fun calculateSelectedX(): Float {
        if (levelPoints.isEmpty()) return 0F
        return if (animatedIndex.toInt() < levelPoints.size - 1) {
            levelPoints[animatedIndex.toInt()] + (animatedIndex - animatedIndex.toInt()) * (levelPoints[animatedIndex.toInt() + 1] - levelPoints[animatedIndex.toInt()])
        } else {
            levelPoints[animatedIndex.toInt()]
        }
    }

    private fun drawGradientLine(canvas: Canvas, lineY: Float, selectedX: Float) {
        val gradientWidth = width * 0.8f
        val gradient = LinearGradient(
            selectedX - gradientWidth / 2, lineY, selectedX + gradientWidth / 2, lineY, intArrayOf(
                Color.parseColor("#494356"), Color.WHITE, Color.parseColor("#494356")
            ), floatArrayOf(0f, 0.5f, 1f), Shader.TileMode.CLAMP
        )

        // 绘制基础线条
        paint.strokeWidth = 2.dp.toFloat()
        paint.shader = null
        paint.color = Color.parseColor("#494356")
        for (i in 0 until levelPoints.size - 1) {
            canvas.drawLine(levelPoints[i], lineY, levelPoints[i + 1], lineY, paint)
        }

        // 绘制渐变线条
        paint.shader = gradient
        paint.strokeWidth = 2.dp.toFloat()
        canvas.drawLine(
            (selectedX - gradientWidth / 2).coerceAtLeast(levelPoints.first()),
            lineY,
            (selectedX + gradientWidth / 2).coerceAtMost(levelPoints.last()),
            lineY,
            paint
        )
        paint.shader = null
    }

    private fun drawCirclesAndLabels(canvas: Canvas, lineY: Float, selectedX: Float) {
        // 为文字设置渐变色
        val textGradient = LinearGradient(
            selectedX - width * 0.4f, lineY + 65.dp, // 文本位置的 Y 值
            selectedX + width * 0.4f, lineY + 65.dp, intArrayOf(
                Color.parseColor("#494356"), Color.WHITE, Color.parseColor("#494356")
            ), floatArrayOf(0f, 0.5f, 1f), Shader.TileMode.CLAMP
        )
        paint.shader = textGradient // 应用于文字

        for (i in levels.indices) {
            var x = levelPoints[i]

            // 计算每个点的 alpha 值，实现淡入淡出效果
            val distanceToAnimated = abs(i - animatedIndex)
            val alpha = (255 * (1 - distanceToAnimated.coerceIn(0f, 1f))).toInt()

            if (i != 0) {

                if (i != levels.size - 1) {
                    // 绘制普通点
                    paint.color = Color.GRAY
                    paint.style = Paint.Style.FILL
                    paint.alpha = 255 - alpha
                    canvas.drawCircle(x, lineY, 3.dp.toFloat(), paint)
                }

                // 绘制发光效果（从外到内多层绘制）
                if (distanceToAnimated < 1f) {

                    if (i == levels.size - 1) {
                        x = x - selectedCircleRadius
                    }
                    val glowAlpha = (alpha * 0.8f).toInt()
                    // 外发光
                    glowPaint.alpha = (glowAlpha * 0.3f).toInt()
                    canvas.drawCircle(x, lineY, selectedCircleRadius * 1.8f, glowPaint)
                    glowPaint.alpha = (glowAlpha * 0.5f).toInt()
                    canvas.drawCircle(x, lineY, selectedCircleRadius * 1.4f, glowPaint)

                    // 内圆
                    paint.color = Color.WHITE
                    paint.alpha = alpha
                    paint.maskFilter = null
                    val currentRadius =
                        3.dp.toFloat() + (selectedCircleRadius - 3.dp.toFloat()) * (1 - distanceToAnimated.coerceIn(
                            0f, 1f
                        ))
                    canvas.drawCircle(x, lineY, currentRadius, paint)
                }
            }

            // 动态调整选中文字的大小和粗细
            paint.maskFilter = null
            if (distanceToAnimated < 0.1f) {
                paint.textSize = 9.sp  // 增加文字大小
                paint.typeface = Typeface.DEFAULT_BOLD  // 设置为加粗
                paint.alpha = 255
                paint.color = Color.WHITE
            } else {
                paint.textSize = 7.sp  // 普通文字大小
                paint.typeface = Typeface.DEFAULT  // 普通粗细
                paint.alpha = 128
                paint.color = Color.GRAY
            }

            // 绘制文本
            canvas.drawText(
                levels[i], x - paint.measureText(levels[i]) / 2, lineY + 25.dp, // 调整文本和线的距离
                paint
            )
        }

        // 重置 shader
        paint.shader = null
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val x = event.x
            for (i in levelPoints.indices) {
                if (x < levelPoints[i] + 50 && x > levelPoints[i] - 50) {
//                    current(i)
                    break
                }
            }
            return true
        }
        return super.onTouchEvent(event)
    }

    fun setupLevel(list: List<String>) {
        this.levels.clear()
        this.levels.add("")
        this.levels.addAll(list)
        this.levels.add("")
        invalidate()
    }

    fun current(index: Int) {
        fadeAnimator?.cancel()

        fadeAnimator = ValueAnimator.ofFloat(animatedIndex, index.toFloat()).apply {
            duration = 300
            interpolator = FastOutSlowInInterpolator()

            addUpdateListener { animation ->
                animatedIndex = animation.animatedValue as Float
                invalidate()
            }
            start()
        }
        selectedLevelIndex = index
    }
}