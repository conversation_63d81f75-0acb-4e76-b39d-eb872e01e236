package com.heart.heartmerge.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.text.isDigitsOnly
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.Quadruple
import com.heart.heartmerge.databinding.ViewLevelCardBinding
import com.heart.heartmerge.mmkv.MMKVDataRep

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/11/8 16:07
 * @description :等级卡片View
 */
class LevelCardView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    val binding: ViewLevelCardBinding =
        ViewLevelCardBinding.inflate(LayoutInflater.from(this.context), this, true)

    val ranges = listOf(
        0..5, 6..10, 11..15, 16..20, 21..25, 26..30, 31..35
    )

    var current: Int = 0
        set(value) {
            field = if (value >= 35) 35 else value
            val levelRange = if (field >= 35) {
                35..Int.MAX_VALUE
            } else {
                ranges.find { ints -> field in ints }
                    .let { ints -> (ints?.start ?: 0)..(ints?.endInclusive ?: 0) }
            }

            if (field in levelRange) {
                val (textColor, labelRes, backgroundRes, progressDrawableRes) = when (levelRange) {
                    ranges[0] -> {
                        Quadruple(
                            R.color.color_level_1,
                            R.mipmap.ic_label_level_1,
                            R.mipmap.bg_level_1,
                            R.drawable.progress_drawable_level_1
                        )
                    }

                    ranges[1] -> Quadruple(
                        R.color.color_level_11,
                        R.mipmap.ic_label_level_11,
                        R.mipmap.bg_level_11,
                        R.drawable.progress_drawable_level_11
                    )

                    ranges[2] -> Quadruple(
                        R.color.color_level_21,
                        R.mipmap.ic_label_level_21,
                        R.mipmap.bg_level_21,
                        R.drawable.progress_drawable_level_21
                    )

                    ranges[3] -> Quadruple(
                        R.color.color_level_31,
                        R.mipmap.ic_label_level_31,
                        R.mipmap.bg_level_31,
                        R.drawable.progress_drawable_level_31
                    )

                    ranges[4] -> Quadruple(
                        R.color.color_level_41,
                        R.mipmap.ic_label_level_41,
                        R.mipmap.bg_level_41,
                        R.drawable.progress_drawable_level_41
                    )

                    ranges[5] -> Quadruple(
                        R.color.color_level_51,
                        R.mipmap.ic_label_level_51,
                        R.mipmap.bg_level_51,
                        R.drawable.progress_drawable_level_51
                    )

                    else -> {
                        Quadruple(
                            R.color.color_level_61,
                            R.mipmap.ic_label_level_61,
                            R.mipmap.bg_level_61,
                            R.drawable.progress_drawable_level_61
                        )
                    }
                }

                binding.tvLevel.setTextCompatColor(textColor)
                binding.tvNextLevelValue.setTextCompatColor(textColor)
                binding.ivLevelLabel.setImageResource(labelRes)
                binding.ivLevel.setImageResource(backgroundRes)
                binding.progressBar.progressDrawable =
                    ContextCompat.getDrawable(this.context, progressDrawableRes)
                binding.tvNotLocked.setTextCompatColor(textColor)
            }

            val userLevel =
                MMKVDataRep.userInfo.takeIf { it.level.isNotEmpty() && it.level.isDigitsOnly() }?.level?.toInt()
                    ?: 0
            binding.tvLevel.apply {
                binding.tvLevel.text = if (userLevel in levelRange) "Lv$userLevel" else "Lv$field"
            }
            binding.tvNotLocked.apply {
                makeVisible(userLevel !in levelRange)
                text = if (current < userLevel) {
                    context.getString(R.string.level_unlocked)
                } else {
                    context.getString(R.string.level_not_unlocked)
                }
            }
            binding.tvNextLevelValue.apply {
                makeVisible(userLevel in levelRange)
                text = context.getString(R.string.to_next_level, MMKVDataRep.userInfo.nextGradeDiff)
            }
            binding.progressBar.apply {
                makeVisible((current <= userLevel) || (userLevel in levelRange))
                if (current < userLevel) {
                    max = 100
                    progress = 100
                } else {
                    max = MMKVDataRep.userInfo.nextGradeStd
                    progress = if (userLevel in levelRange) MMKVDataRep.userInfo.diamondTotal else 0
                }
            }
        }
}