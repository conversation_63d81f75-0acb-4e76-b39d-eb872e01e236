package com.heart.heartmerge.ui.activities.mingle

import android.os.Bundle
import coil.load
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.databinding.ActivityDialogMatchTipBinding
import com.heart.heartmerge.firebase.report.DT_EVENT_CONSTANTS
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.fromJson
import com.heart.heartmerge.viewmodes.AnchorVideoViewModel


/**
 * Author:Lxf
 * Create on:2024/8/15
 * Description:
 */
class MatchTipDialogActivity :
    BaseCoreActivity<ActivityDialogMatchTipBinding, AnchorVideoViewModel>() {
    private val anchorInfo: UserBean?
        get() = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO)?.fromJson<UserBean>()

    override fun getLayoutId(): Int = R.layout.activity_dialog_match_tip
    override fun initView() {
        anchorInfo?.apply {
            mBinding.anchorName.text = nickName
            mBinding.anchorImage.load(headFileName) {
                crossfade(true)
                placeholder(R.mipmap.ic_default_anchor_bg)
                error(R.mipmap.ic_default_anchor_bg)
            }
            mBinding.videoPrice.text =
                String.format(getString(R.string.label_diamond_every_min), videoPrice)
            mBinding.anchorAgeCountry.text =
                String.format(getString(R.string.video_anchor_other_info), age, country)
        }
    }

    override fun bindListener() {
//        mBinding.tipParent.click {
//            finish()
//        }
        mBinding.btnCancel.click {
            finish()
        }
        mBinding.btnOk.click {
            gotoVideo()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    private fun gotoVideo() {
        ReportManager.logEvent(DT_EVENT_CONSTANTS.EVENT_VIDEO_CALL, buildMap {
            put("userId", MMKVDataRep.userInfo.id)
            put("anchor", intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO) ?: "")
            put(
                "useMatchCard", intent.getBooleanExtra(Constants.INTENT_PARAM_USE_MATCH_CARD, false)
            )
            put("videoSource", Constants.VIDEO_SOURCE_MATCH)
        })
        AppUtil.requestVideoPermission(this, onGrantedCallBack = {
            jump(AnchorVideoActivity::class.java, Bundle().apply {
                putString(
                    Constants.INTENT_PARAM_KEY_ANCHOR_INFO,
                    intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO)
                )
                putBoolean(
                    Constants.INTENT_PARAM_USE_MATCH_CARD,
                    intent.getBooleanExtra(Constants.INTENT_PARAM_USE_MATCH_CARD, false)
                )
                putString(Constants.INTENT_PARAM_VIDEO_SOURCE, Constants.VIDEO_SOURCE_MATCH) //来源匹配
            })
            finish()
        }) {}


    }

}