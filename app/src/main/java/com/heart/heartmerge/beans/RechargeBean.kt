package com.heart.heartmerge.beans

import com.android.billingclient.api.ProductDetails

data class RechargeBean(
    val id: String,
    val num: Int,
    val giveNum: Int,
    val currency: String,
    val price: String,
    val originPrice: Double,
    val dollarPrice: Double,
    val productId: String,
    val googleProductId: String,
    val itemName: String,
    val country: String,
    val message: String,
    val priceType: String,/*1-钻石消耗,2-会员订阅,3-全部*/
    val discountType: String,/*0-无优惠,2-包含优惠*/
    val discountMessage: String,
    val imageUrl: String,
    val payTypeMessage: String,
    val payMethodId: String,
    val actualAmount: String,//实际显示金额
    val orderStatus: String,/*1-成功，2-未成功*/
    val packageName: String,
    val realDiamondNum: String,//钻石数量
    val level: String,
    val createTime: String,
    var times: Long,
    var countdown: String,

    /*谷歌商品扩展参数*/
    var googleExtras: ProductDetails? = null
) {
    val isSubscribe get() = priceType == "2"

    val hasDiscount get() = discountType != "0" && discountType != ""

    val formattedPrice get() = "$currency $price"
}
