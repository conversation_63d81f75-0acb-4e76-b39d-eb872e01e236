package com.heart.heartmerge.utils

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner

/**
 * 生命周期安全工具类
 * 用于在RecyclerView的Item中安全地处理生命周期相关操作
 * 
 * 作者：AI Assistant
 * 创建日期：2025/1/25
 * 描述：解决DslAdapterItem生命周期状态异常问题
 */
object LifecycleSafetyUtils {
    
    /**
     * 安全地执行需要生命周期检查的操作
     * 
     * @param lifecycleOwner 生命周期拥有者
     * @param minState 最小生命周期状态要求
     * @param action 需要执行的操作
     * @param fallbackAction 当生命周期不满足条件时的备用操作
     */
    inline fun safeExecuteWithLifecycle(
        lifecycleOwner: LifecycleOwner?,
        minState: Lifecycle.State = Lifecycle.State.CREATED,
        action: () -> Unit,
        fallbackAction: (() -> Unit)? = null
    ) {
        try {
            lifecycleOwner?.lifecycle?.let { lifecycle ->
                // 检查生命周期状态是否有效
                if (lifecycle.currentState != Lifecycle.State.DESTROYED && 
                    lifecycle.currentState.isAtLeast(minState)) {
                    action()
                } else {
                    fallbackAction?.invoke()
                }
            } ?: run {
                // 如果没有生命周期管理，执行备用操作
                fallbackAction?.invoke() ?: action()
            }
        } catch (e: Exception) {
            // 捕获生命周期状态异常，避免崩溃
            e.printStackTrace()
            // 尝试执行备用操作
            try {
                fallbackAction?.invoke()
            } catch (fallbackException: Exception) {
                fallbackException.printStackTrace()
            }
        }
    }
    
    /**
     * 检查生命周期是否处于安全状态
     * 
     * @param lifecycleOwner 生命周期拥有者
     * @param minState 最小生命周期状态要求
     * @return 是否处于安全状态
     */
    fun isLifecycleSafe(
        lifecycleOwner: LifecycleOwner?,
        minState: Lifecycle.State = Lifecycle.State.CREATED
    ): Boolean {
        return try {
            lifecycleOwner?.lifecycle?.let { lifecycle ->
                lifecycle.currentState != Lifecycle.State.DESTROYED && 
                lifecycle.currentState.isAtLeast(minState)
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 安全地启动SVGA动画
     * 
     * @param lifecycleOwner 生命周期拥有者
     * @param svgaView SVGA视图
     * @param forceStart 是否强制启动（忽略生命周期检查）
     */
    fun safeStartSvgaAnimation(
        lifecycleOwner: LifecycleOwner?,
        svgaView: Any?, // 使用Any避免直接依赖SVGA库
        forceStart: Boolean = false
    ) {
        try {
            if (forceStart) {
                // 强制启动，不检查生命周期
                (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.startAnimation()
            } else {
                // 安全启动，检查生命周期
                safeExecuteWithLifecycle(
                    lifecycleOwner = lifecycleOwner,
                    minState = Lifecycle.State.RESUMED,
                    action = {
                        (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.startAnimation()
                    },
                    fallbackAction = {
                        // 如果生命周期不满足条件，也可以选择启动动画
                        (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.startAnimation()
                    }
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 安全地停止SVGA动画
     * 
     * @param lifecycleOwner 生命周期拥有者
     * @param svgaView SVGA视图
     * @param forceStop 是否强制停止（忽略生命周期检查）
     */
    fun safeStopSvgaAnimation(
        lifecycleOwner: LifecycleOwner?,
        svgaView: Any?, // 使用Any避免直接依赖SVGA库
        forceStop: Boolean = true
    ) {
        try {
            if (forceStop) {
                // 强制停止，不检查生命周期
                (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.stopAnimation()
            } else {
                // 安全停止，检查生命周期
                safeExecuteWithLifecycle(
                    lifecycleOwner = lifecycleOwner,
                    minState = Lifecycle.State.CREATED,
                    action = {
                        (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.stopAnimation()
                    },
                    fallbackAction = {
                        // 即使生命周期不满足条件，也要停止动画避免内存泄漏
                        (svgaView as? com.opensource.svgaplayer.SVGAImageView)?.stopAnimation()
                    }
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
