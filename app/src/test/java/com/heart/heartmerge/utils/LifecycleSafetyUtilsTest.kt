package com.heart.heartmerge.utils

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*

/**
 * LifecycleSafetyUtils 单元测试
 * 
 * 作者：AI Assistant
 * 创建日期：2025/1/25
 * 描述：测试生命周期安全工具类的各种场景
 */
class LifecycleSafetyUtilsTest {

    @Test
    fun `test isLifecycleSafe with valid lifecycle`() {
        // 创建模拟的LifecycleOwner
        val lifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycle = mock(Lifecycle::class.java)
        
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycle)
        `when`(lifecycle.currentState).thenReturn(Lifecycle.State.RESUMED)
        
        // 测试正常状态
        assertTrue(LifecycleSafetyUtils.isLifecycleSafe(lifecycleOwner, Lifecycle.State.CREATED))
        assertTrue(LifecycleSafetyUtils.isLifecycleSafe(lifecycleOwner, Lifecycle.State.RESUMED))
    }

    @Test
    fun `test isLifecycleSafe with destroyed lifecycle`() {
        // 创建模拟的LifecycleOwner
        val lifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycle = mock(Lifecycle::class.java)
        
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycle)
        `when`(lifecycle.currentState).thenReturn(Lifecycle.State.DESTROYED)
        
        // 测试销毁状态
        assertFalse(LifecycleSafetyUtils.isLifecycleSafe(lifecycleOwner, Lifecycle.State.CREATED))
    }

    @Test
    fun `test isLifecycleSafe with null lifecycle owner`() {
        // 测试空的LifecycleOwner
        assertFalse(LifecycleSafetyUtils.isLifecycleSafe(null, Lifecycle.State.CREATED))
    }

    @Test
    fun `test safeExecuteWithLifecycle with valid lifecycle`() {
        // 创建模拟的LifecycleOwner
        val lifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycle = mock(Lifecycle::class.java)
        
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycle)
        `when`(lifecycle.currentState).thenReturn(Lifecycle.State.RESUMED)
        
        var actionExecuted = false
        var fallbackExecuted = false
        
        // 测试正常执行
        LifecycleSafetyUtils.safeExecuteWithLifecycle(
            lifecycleOwner = lifecycleOwner,
            minState = Lifecycle.State.CREATED,
            action = { actionExecuted = true },
            fallbackAction = { fallbackExecuted = true }
        )
        
        assertTrue("Action should be executed", actionExecuted)
        assertFalse("Fallback should not be executed", fallbackExecuted)
    }

    @Test
    fun `test safeExecuteWithLifecycle with destroyed lifecycle`() {
        // 创建模拟的LifecycleOwner
        val lifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycle = mock(Lifecycle::class.java)
        
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycle)
        `when`(lifecycle.currentState).thenReturn(Lifecycle.State.DESTROYED)
        
        var actionExecuted = false
        var fallbackExecuted = false
        
        // 测试销毁状态下的执行
        LifecycleSafetyUtils.safeExecuteWithLifecycle(
            lifecycleOwner = lifecycleOwner,
            minState = Lifecycle.State.CREATED,
            action = { actionExecuted = true },
            fallbackAction = { fallbackExecuted = true }
        )
        
        assertFalse("Action should not be executed", actionExecuted)
        assertTrue("Fallback should be executed", fallbackExecuted)
    }

    @Test
    fun `test safeExecuteWithLifecycle with null lifecycle owner`() {
        var actionExecuted = false
        var fallbackExecuted = false
        
        // 测试空的LifecycleOwner
        LifecycleSafetyUtils.safeExecuteWithLifecycle(
            lifecycleOwner = null,
            minState = Lifecycle.State.CREATED,
            action = { actionExecuted = true },
            fallbackAction = { fallbackExecuted = true }
        )
        
        assertFalse("Action should not be executed", actionExecuted)
        assertTrue("Fallback should be executed", fallbackExecuted)
    }

    @Test
    fun `test safeExecuteWithLifecycle with exception handling`() {
        // 创建模拟的LifecycleOwner，让它抛出异常
        val lifecycleOwner = mock(LifecycleOwner::class.java)
        `when`(lifecycleOwner.lifecycle).thenThrow(RuntimeException("Test exception"))
        
        var actionExecuted = false
        var fallbackExecuted = false
        
        // 测试异常处理
        LifecycleSafetyUtils.safeExecuteWithLifecycle(
            lifecycleOwner = lifecycleOwner,
            minState = Lifecycle.State.CREATED,
            action = { actionExecuted = true },
            fallbackAction = { fallbackExecuted = true }
        )
        
        assertFalse("Action should not be executed due to exception", actionExecuted)
        assertTrue("Fallback should be executed due to exception", fallbackExecuted)
    }
}
