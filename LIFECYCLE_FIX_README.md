# DslAdapterItem 生命周期状态异常修复

## 问题描述

在使用 DslAdapter 7.0.0 版本时，出现了以下生命周期状态异常：

```
java.lang.IllegalStateException: State is 'DESTROYED' and cannot be moved to `CREATED` in component com.heart.heartmerge.ui.fragments.home.HomeAnchorItem@62b5fa6
```

## 问题根因

1. **触发场景**：RecyclerView 滚动时，视图被回收，触发 `onItemViewDetachedToWindow` 回调
2. **根本原因**：DslAdapterItem 内部的生命周期管理机制试图将已经 `DESTROYED` 的生命周期状态重新设置为 `CREATED`
3. **具体位置**：`HomeAnchorItem.onItemViewDetachedToWindow` 方法中访问生命周期状态时

## 解决方案

### 1. 创建生命周期安全工具类

创建了 `LifecycleSafetyUtils` 工具类，提供以下功能：

- **安全执行**：`safeExecuteWithLifecycle()` - 在执行生命周期相关操作前进行状态检查
- **状态检查**：`isLifecycleSafe()` - 检查生命周期是否处于安全状态
- **SVGA动画管理**：`safeStartSvgaAnimation()` 和 `safeStopSvgaAnimation()` - 安全地管理SVGA动画

### 2. 修复 HomeAnchorItem 类

#### 修复前的问题代码：
```kotlin
override fun onItemViewDetachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
    super.onItemViewDetachedToWindow(itemHolder, itemPosition)
    val svgaImageView = itemHolder.view(R.id.anchor_video) as SVGAImageView
    if (svgaImageView.isVisible()) {
        (itemHolder.context as? LifecycleOwner)?.lifecycle?.let { lifecycle ->
            if (svgaImageView.isVisible()) {
                svgaImageView.stopAnimation() // 可能触发生命周期状态异常
            }
        }
    }
}
```

#### 修复后的安全代码：
```kotlin
override fun onItemViewDetachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
    try {
        val svgaImageView = itemHolder.view(R.id.anchor_video) as? SVGAImageView
        if (svgaImageView?.isVisible() == true) {
            // 使用生命周期安全工具类停止动画
            LifecycleSafetyUtils.safeStopSvgaAnimation(
                lifecycleOwner = itemHolder.context as? LifecycleOwner,
                svgaView = svgaImageView,
                forceStop = true // 强制停止动画，避免内存泄漏
            )
        }
    } catch (e: Exception) {
        e.printStackTrace()
    } finally {
        // 确保父类方法在最后调用，避免生命周期状态冲突
        super.onItemViewDetachedToWindow(itemHolder, itemPosition)
    }
}
```

### 3. 关键修复点

1. **状态检查**：在访问生命周期前检查是否为 `DESTROYED` 状态
2. **异常处理**：添加 try-catch 块捕获生命周期状态异常
3. **调用顺序**：将 `super.onItemViewDetachedToWindow()` 放在 finally 块中最后调用
4. **强制停止**：在视图分离时强制停止动画，避免内存泄漏
5. **安全转换**：使用安全转换 `as?` 替代强制转换 `as`

## 测试验证

创建了单元测试 `LifecycleSafetyUtilsTest` 来验证工具类的各种场景：

- ✅ 正常生命周期状态下的操作
- ✅ DESTROYED 状态下的安全处理
- ✅ 空 LifecycleOwner 的处理
- ✅ 异常情况的处理

## 使用建议

### 对于新的 DslAdapterItem 子类：

1. **优先使用工具类**：使用 `LifecycleSafetyUtils` 进行生命周期相关操作
2. **添加异常处理**：在 `onItemViewDetachedToWindow` 中添加 try-catch
3. **调用顺序**：确保 `super` 方法在最后调用
4. **安全转换**：使用 `as?` 进行安全类型转换

### 示例代码：
```kotlin
override fun onItemViewDetachedToWindow(itemHolder: DslViewHolder, itemPosition: Int) {
    try {
        // 你的清理逻辑
        LifecycleSafetyUtils.safeExecuteWithLifecycle(
            lifecycleOwner = itemHolder.context as? LifecycleOwner,
            action = { /* 需要生命周期检查的操作 */ },
            fallbackAction = { /* 备用操作 */ }
        )
    } catch (e: Exception) {
        e.printStackTrace()
    } finally {
        super.onItemViewDetachedToWindow(itemHolder, itemPosition)
    }
}
```

## 相关文件

- `app/src/main/java/com/heart/heartmerge/ui/fragments/home/<USER>
- `app/src/main/java/com/heart/heartmerge/utils/LifecycleSafetyUtils.kt` - 生命周期安全工具类
- `app/src/test/java/com/heart/heartmerge/utils/LifecycleSafetyUtilsTest.kt` - 单元测试

## 预防措施

1. **代码审查**：在代码审查时重点关注 DslAdapterItem 子类的生命周期操作
2. **测试覆盖**：为涉及生命周期的组件添加单元测试
3. **监控告警**：在生产环境中监控此类异常的发生频率
4. **文档更新**：更新开发规范，要求使用生命周期安全工具类

通过以上修复，应该能够彻底解决 DslAdapterItem 生命周期状态异常问题。
